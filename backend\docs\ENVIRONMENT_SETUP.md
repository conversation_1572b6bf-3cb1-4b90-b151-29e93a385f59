# Настройка переменных окружения

Этот документ описывает, как настроить переменные окружения для системы рекомендаций игр.

## Быстрый старт

1. **Скопируйте файл примера:**
   ```bash
   cp .env.example .env
   ```

2. **Сгенерируйте секретный ключ:**
   ```bash
   python scripts/generate_secret_key.py
   ```

3. **Отредактируйте файл .env** и установите нужные значения.

## Обязательные переменные

### SECRET_KEY
**Описание:** Секретный ключ для подписи JWT токенов  
**Обязательно:** Да  
**Пример:** `SECRET_KEY=your-generated-secret-key-here`

⚠️ **ВАЖНО:** Никогда не используйте значение по умолчанию в продакшене!

### DATABASE_URL
**Описание:** URL подключения к основной базе данных  
**По умолчанию:** `sqlite:///src/database.db`  
**Пример:** `DATABASE_URL=sqlite:///src/database.db`

## Опциональные переменные

### Сервер
- `HOST` - Хост сервера (по умолчанию: `127.0.0.1`)
- `PORT` - Порт сервера (по умолчанию: `8000`)
- `DEBUG` - Режим отладки (по умолчанию: `false`)

### Безопасность
- `ACCESS_TOKEN_EXPIRE_MINUTES` - Время жизни токенов в минутах (по умолчанию: `30`)

### CORS
- `ALLOWED_ORIGINS` - Разрешенные домены для CORS (по умолчанию: `*`)

### База данных
- `DB_PATH` - Путь к базе данных с играми (по умолчанию: `data/games.db`)

### Кэширование
- `CACHE_DIR` - Директория для кэша (по умолчанию: `src/cache`)

### Внешние API
- `STEAM_API_TIMEOUT` - Таймаут для Steam API в секундах (по умолчанию: `10.0`)

## Примеры конфигураций

### Разработка
```env
SECRET_KEY=dev-secret-key-change-in-production
DEBUG=true
HOST=127.0.0.1
PORT=8000
ALLOWED_ORIGINS=*
```

### Продакшен
```env
SECRET_KEY=your-super-secure-production-key-here
DEBUG=false
HOST=0.0.0.0
PORT=80
ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
```

### Тестирование
```env
SECRET_KEY=test-secret-key
DEBUG=true
DATABASE_URL=sqlite:///test_database.db
PORT=8001
```

## Безопасность

### Генерация секретного ключа
Используйте скрипт для генерации безопасного ключа:
```bash
python scripts/generate_secret_key.py
```

### Рекомендации по безопасности
1. **Никогда не коммитьте .env файл** в систему контроля версий
2. **Используйте разные ключи** для разработки и продакшена
3. **Регулярно меняйте ключи** в продакшене
4. **Ограничьте CORS** в продакшене до конкретных доменов
5. **Используйте HTTPS** в продакшене

## Проверка конфигурации

Запустите приложение и проверьте логи:
```bash
python run.py
```

В логах должно отображаться:
- Используемый хост и порт
- Режим отладки
- Успешное подключение к базе данных

## Устранение проблем

### Ошибка "SECRET_KEY должен быть установлен в продакшене!"
**Решение:** Установите уникальный SECRET_KEY в файле .env

### Ошибка подключения к базе данных
**Решение:** Проверьте правильность DATABASE_URL и наличие файла базы данных

### Проблемы с CORS
**Решение:** Проверьте настройку ALLOWED_ORIGINS для вашего домена

## Переменные окружения в Docker

При использовании Docker, передавайте переменные через docker-compose.yml:

```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
    env_file:
      - .env
```

## Переменные окружения в облаке

### Heroku
```bash
heroku config:set SECRET_KEY=your-secret-key
heroku config:set DEBUG=false
```

### AWS/Azure/GCP
Используйте соответствующие сервисы управления секретами:
- AWS Secrets Manager
- Azure Key Vault  
- Google Secret Manager
