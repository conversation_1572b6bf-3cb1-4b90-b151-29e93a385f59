"""
Простой скрипт для отображения всех таблиц в базе данных SQLite
"""
import sqlite3

# Путь к базе данных
DB_PATH = "data/games.db"

try:
    # Подключаемся к базе данных
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Получаем список всех таблиц
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = cursor.fetchall()
    
    print(f"База данных: {DB_PATH}")
    print(f"Найдено {len(tables)} таблиц:")
    
    for i, table in enumerate(tables, 1):
        table_name = table[0]
        
        # Получаем структуру таблицы
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # Получаем количество записей в таблице
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        print(f"\n{i}. Таблица: {table_name}")
        print(f"   Количество записей: {count}")
        print("   Столбцы:")
        for col in columns:
            col_id, col_name, col_type, not_null, default_val, is_pk = col
            pk_mark = " (PK)" if is_pk else ""
            print(f"     - {col_name} ({col_type}){pk_mark}")
    
    conn.close()
except Exception as e:
    print(f"Ошибка: {str(e)}")
