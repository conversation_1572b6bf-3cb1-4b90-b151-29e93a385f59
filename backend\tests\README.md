# Tests Directory

Комплексная система тестирования для системы рекомендаций игр Steam.

## Файлы тестов

### 🧪 test_comprehensive.py
Комплексные функциональные тесты всех основных возможностей системы:
- ✅ Проверка доступности приложения
- 👤 Регистрация и аутентификация пользователей
- 🎮 Поиск и фильтрация игр
- 🤖 Система рекомендаций (общие и персональные)
- 📝 Управление списками игр пользователей
- ⭐ Оценки игр пользователями
- 🖼️ Интеграция с Steam API

### ⚡ test_performance.py
Тесты производительности и нагрузочной способности:
- 📊 Измерение времени отклика эндпоинтов
- 🚀 Нагрузочное тестирование с параллельными запросами
- 📈 Анализ пропускной способности
- 💡 Рекомендации по оптимизации

### 🎯 run_tests.py
Главный скрипт-запускатель для всех тестов:
- 🔄 Автоматический запуск всех типов тестов
- 🎛️ Гибкие опции запуска
- 📋 Итоговая отчетность
- 🔍 Проверка готовности системы

## Быстрый старт

### Запуск всех тестов
```bash
python tests/run_tests.py
```

### Только функциональные тесты
```bash
python tests/run_tests.py --comprehensive
```

### Только тесты производительности
```bash
python tests/run_tests.py --performance
```

### Быстрая проверка
```bash
python tests/run_tests.py --quick
```

## Детальное использование

### Комплексные тесты
```bash
# Запуск с детальным выводом
python tests/test_comprehensive.py

# Справка
python tests/test_comprehensive.py --help
```

### Тесты производительности
```bash
# Запуск тестов производительности
python tests/test_performance.py

# Справка
python tests/test_performance.py --help
```

## Требования

### Перед запуском тестов убедитесь:
1. **Приложение запущено**: `python run.py`
2. **База данных доступна**: файл `data/games.db` существует
3. **Зависимости установлены**: `pip install -r requirements.txt`
4. **Порт 8000 свободен**: приложение должно быть доступно на `http://127.0.0.1:8000`

## Результаты тестов

### Вывод в консоль
- ✅/❌ Статус каждого теста в реальном времени
- 📊 Статистика выполнения
- 💡 Рекомендации по исправлению ошибок

### Файлы отчетов
- `test_report_*.json` - Детальные отчеты комплексных тестов
- JSON формат для автоматической обработки
- Временные метки и подробная диагностика

## Интерпретация результатов

### Комплексные тесты
- **✅ PASS** - Тест прошел успешно
- **❌ FAIL** - Тест провалился, требует внимания
- **Процент успеха** - Общая оценка работоспособности системы

### Тесты производительности
- **Время отклика** - Среднее время ответа эндпоинта (цель: <1000ms)
- **Пропускная способность** - Количество запросов в секунду (цель: >1 req/s)
- **Ошибки** - Количество неуспешных запросов (цель: 0)

## Устранение проблем

### Частые проблемы
1. **Приложение недоступно**
   - Проверьте: `python run.py`
   - Убедитесь что порт 8000 свободен

2. **Ошибки базы данных**
   - Проверьте файл `data/games.db`
   - Запустите миграции: `python src/migrate_db.py`

3. **Ошибки аутентификации**
   - Проверьте настройки в `src/auth.py`
   - Убедитесь что таблица users существует

4. **Медленная работа**
   - Проверьте размер базы данных
   - Рассмотрите оптимизацию запросов
   - Проверьте доступность кэша

## Расширение тестов

### Добавление новых тестов
1. Создайте новый метод в `GameRecommendationTester`
2. Добавьте его в список `tests` в методе `run_all_tests`
3. Следуйте паттерну логирования с `self.log_test`

### Добавление тестов производительности
1. Добавьте новый эндпоинт в `endpoints_to_test`
2. Или создайте новый нагрузочный тест в `load_tests`

## Автоматизация

### CI/CD интеграция
```bash
# Пример для GitHub Actions
python tests/run_tests.py --skip-check
```

### Мониторинг
- Используйте коды возврата для автоматических проверок
- Анализируйте JSON отчеты для трендов производительности
