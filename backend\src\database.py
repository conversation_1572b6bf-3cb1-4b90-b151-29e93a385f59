from sqlmodel import SQLModel, create_engine, Session
from typing import Generator
from src.config import settings, get_database_url

# Создаем URL для подключения к базе данных SQLite
DATABASE_URL = get_database_url()

# Создаем движок для работы с базой данных
# В режиме разработки включаем echo для отладки SQL запросов
engine = create_engine(DATABASE_URL, echo=settings.DEBUG)

def create_db_and_tables():
    """Создает базу данных и таблицы"""
    SQLModel.metadata.create_all(engine)

def get_session() -> Generator[Session, None, None]:
    """Возвращает сессию для работы с базой данных"""
    with Session(engine) as session:
        yield session
