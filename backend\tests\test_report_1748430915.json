{"timestamp": "2025-05-28 14:15:15", "summary": {"total_tests": 13, "passed": 7, "failed": 6}, "test_results": [{"test": "Health Check", "success": true, "message": "Status code: 200", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "User Registration", "success": false, "message": "Registration failed: 500", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "User Login", "success": false, "message": "Exception: (sqlite3.OperationalError) no such column: users.avatar\n[SQL: SELECT users.username, users.email, users.is_active, users.id, users.hashed_password, users.avatar, users.avatar_content_type \nFROM users \nWHERE users.username = ?]\n[parameters: ('test_user_1748430911',)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "User Profile", "success": false, "message": "No access token available", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "Games List", "success": true, "message": "Retrieved 10 games", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "Game Search", "success": true, "message": "Search returned 51 results", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "Autocomplete", "success": true, "message": "Autocomplete returned 5 suggestions", "details": null, "timestamp": "2025-05-28 14:15:11"}, {"test": "Game Recommendations", "success": true, "message": "Got 5 recommendations", "details": null, "timestamp": "2025-05-28 14:15:14"}, {"test": "Game Details", "success": true, "message": "Game details for 'Counter-Strike', image: True", "details": null, "timestamp": "2025-05-28 14:15:15"}, {"test": "Game Filters", "success": true, "message": "<PERSON><PERSON> returned 0 games", "details": null, "timestamp": "2025-05-28 14:15:15"}, {"test": "User Rating", "success": false, "message": "No access token available", "details": null, "timestamp": "2025-05-28 14:15:15"}, {"test": "Game List Management", "success": false, "message": "No access token available", "details": null, "timestamp": "2025-05-28 14:15:15"}, {"test": "Personal Recommendations", "success": false, "message": "No access token available", "details": null, "timestamp": "2025-05-28 14:15:15"}]}