#!/usr/bin/env python3
"""
Тест продакшен режима
"""
import os
import sys

# Устанавливаем переменные окружения напрямую для тестирования продакшена
os.environ['DEBUG'] = 'false'
os.environ['SECRET_KEY'] = 'test-production-key-12345678901234567890'
os.environ['HOST'] = '0.0.0.0'
os.environ['PORT'] = '8000'
os.environ['ALLOWED_ORIGINS'] = 'https://example.com,https://api.example.com'

# Очищаем кэш модулей, чтобы перезагрузить конфигурацию
modules_to_clear = [mod for mod in sys.modules.keys() if mod.startswith('src.config')]
for mod in modules_to_clear:
    del sys.modules[mod]

try:
    from src.config import settings
    print("✅ Конфигурация продакшена загружена успешно!")
    print(f"DEBUG: {settings.DEBUG}")
    print(f"HOST: {settings.HOST}")
    print(f"PORT: {settings.PORT}")
    print(f"ALLOWED_ORIGINS: {settings.ALLOWED_ORIGINS}")
    print(f"SECRET_KEY установлен: {'Да' if settings.SECRET_KEY else 'Нет'}")

    if settings.DEBUG:
        print("❌ ОШИБКА: DEBUG режим включен в продакшене!")
        sys.exit(1)
    else:
        print("✅ DEBUG режим выключен")

    if settings.ALLOWED_ORIGINS == ["*"]:
        print("⚠️  ПРЕДУПРЕЖДЕНИЕ: CORS разрешен для всех доменов")
    else:
        print("✅ CORS настроен для конкретных доменов")

    print("✅ Продакшен конфигурация корректна!")

except Exception as e:
    print(f"❌ Ошибка загрузки конфигурации: {e}")
    sys.exit(1)
