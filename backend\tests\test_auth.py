import os
import sys

# Add the current directory to the Python path
sys.path.append(os.path.abspath('.'))

from sqlmodel import Session, select
from src.database import create_db_and_tables, engine
from src.models.models import User, UserRating, GameList, GameListType, Game
from src.auth import get_password_hash

def test_create_tables():
    """Test creating all tables"""
    print("Creating tables...")
    create_db_and_tables()
    print("Tables created successfully")

def test_create_user():
    """Test creating a user"""
    print("Creating a test user...")
    with Session(engine) as session:
        # Check if user already exists
        user = session.exec(select(User).where(User.username == "testuser")).first()
        if user:
            print(f"User {user.username} already exists with ID {user.id}")
        else:
            # Create a new user
            hashed_password = get_password_hash("testpassword")
            new_user = User(
                username="testuser",
                email="<EMAIL>",
                hashed_password=hashed_password,
                is_active=True
            )
            session.add(new_user)
            session.commit()
            session.refresh(new_user)
            print(f"Created user {new_user.username} with ID {new_user.id}")

def test_add_rating():
    """Test adding a rating"""
    print("Adding a test rating...")
    with Session(engine) as session:
        # Get the test user
        user = session.exec(select(User).where(User.username == "testuser")).first()
        if not user:
            print("Test user not found")
            return

        # Get a game to rate
        game = session.exec(select(Game)).first()
        if not game:
            print("No games found in the database")
            return

        # Check if rating already exists
        existing_rating = session.exec(
            select(UserRating).where(
                (UserRating.user_id == user.id) & 
                (UserRating.app_id == game.app_id)
            )
        ).first()

        if existing_rating:
            print(f"Rating already exists: {existing_rating.rating} for game {game.title}")
            # Update the rating
            existing_rating.rating = 5
            session.add(existing_rating)
            session.commit()
            print(f"Updated rating to 5 for game {game.title}")
        else:
            # Add a new rating
            new_rating = UserRating(user_id=user.id, app_id=game.app_id, rating=5)
            session.add(new_rating)
            session.commit()
            print(f"Added rating 5 for game {game.title}")

def test_add_game_to_list():
    """Test adding a game to a list"""
    print("Adding a game to a list...")
    with Session(engine) as session:
        # Get the test user
        user = session.exec(select(User).where(User.username == "testuser")).first()
        if not user:
            print("Test user not found")
            return

        # Get a game to add to the list
        game = session.exec(select(Game)).first()
        if not game:
            print("No games found in the database")
            return

        # Check if game already in a list
        existing_list = session.exec(
            select(GameList).where(
                (GameList.user_id == user.id) & 
                (GameList.app_id == game.app_id)
            )
        ).first()

        if existing_list:
            print(f"Game {game.title} already in list {existing_list.list_type}")
            # Update the list type
            existing_list.list_type = GameListType.COMPLETED
            session.add(existing_list)
            session.commit()
            print(f"Updated list type to {GameListType.COMPLETED} for game {game.title}")
        else:
            # Add game to list
            new_list_item = GameList(
                user_id=user.id,
                app_id=game.app_id,
                list_type=GameListType.PLAYING
            )
            session.add(new_list_item)
            session.commit()
            print(f"Added game {game.title} to list {GameListType.PLAYING}")

def test_get_user_ratings():
    """Test getting user ratings"""
    print("Getting user ratings...")
    with Session(engine) as session:
        # Get the test user
        user = session.exec(select(User).where(User.username == "testuser")).first()
        if not user:
            print("Test user not found")
            return

        # Get user ratings
        ratings_query = select(UserRating, Game).join(Game).where(UserRating.user_id == user.id)
        ratings_result = session.exec(ratings_query).all()

        if not ratings_result:
            print("No ratings found for the user")
            return

        print(f"Found {len(ratings_result)} ratings:")
        for rating, game in ratings_result:
            print(f"- {game.title}: {rating.rating}")

def test_get_user_game_lists():
    """Test getting user game lists"""
    print("Getting user game lists...")
    with Session(engine) as session:
        # Get the test user
        user = session.exec(select(User).where(User.username == "testuser")).first()
        if not user:
            print("Test user not found")
            return

        # Get user game lists
        lists_query = select(GameList, Game).join(Game).where(GameList.user_id == user.id)
        lists_result = session.exec(lists_query).all()

        if not lists_result:
            print("No game lists found for the user")
            return

        # Group by list type
        playing = []
        planned = []
        completed = []

        for list_item, game in lists_result:
            if list_item.list_type == GameListType.PLAYING:
                playing.append(game.title)
            elif list_item.list_type == GameListType.PLANNED:
                planned.append(game.title)
            elif list_item.list_type == GameListType.COMPLETED:
                completed.append(game.title)

        print(f"Playing ({len(playing)}): {', '.join(playing)}")
        print(f"Planned ({len(planned)}): {', '.join(planned)}")
        print(f"Completed ({len(completed)}): {', '.join(completed)}")

if __name__ == "__main__":
    test_create_tables()
    test_create_user()
    test_add_rating()
    test_add_game_to_list()
    test_get_user_ratings()
    test_get_user_game_lists()
    print("All tests completed successfully")
