"""
Конфигурация приложения с использованием переменных окружения
"""
import os
from pathlib import Path

try:
    from pydantic_settings import BaseSettings
    from pydantic import field_validator
except ImportError:
    from pydantic import BaseSettings
    from pydantic import validator as field_validator


class Settings(BaseSettings):
    """Настройки приложения"""

    # Безопасность
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # База данных
    DATABASE_URL: str = "sqlite:///src/database.db"
    DB_PATH: str = "data/games.db"

    # Сервер
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    DEBUG: bool = False

    # CORS
    ALLOWED_ORIGINS: str = "*"

    # Кэш
    CACHE_DIR: str = "src/cache"

    # Внешние API
    STEAM_API_TIMEOUT: float = 10.0

    # Настройки приложения
    APP_TITLE: str = "Система рекомендаций игр"
    APP_DESCRIPTION: str = "Асинхронный API для рекомендации игр с использованием гибридного метода рекомендаций"

    # Обработка переменных
    @field_validator('ALLOWED_ORIGINS')
    @classmethod
    def parse_cors_origins(cls, v):
        """Парсит список разрешенных доменов для CORS"""
        if v == "*":
            return ["*"]
        return [origin.strip() for origin in v.split(",")]

    @field_validator('SECRET_KEY')
    @classmethod
    def validate_secret_key(cls, v):
        """Проверяет, что секретный ключ не является значением по умолчанию в продакшене"""
        if v == "your-secret-key-here" and not os.getenv("DEBUG", "false").lower() == "true":
            raise ValueError("SECRET_KEY должен быть установлен в продакшене!")
        return v

    @field_validator('DATABASE_URL')
    @classmethod
    def fix_sqlite_path(cls, v):
        """Обеспечивает абсолютный путь для SQLite"""
        if v.startswith("sqlite:///"):
            rel_path = v.replace("sqlite:///", "", 1)
            abs_path = Path(__file__).resolve().parent.parent / rel_path
            return f"sqlite:///{abs_path}"
        return v

    class Config:
        env_file = Path(__file__).resolve().parent.parent / ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Создаем глобальный экземпляр настроек
settings = Settings()


def get_database_url() -> str:
    """Возвращает URL базы данных"""
    return settings.DATABASE_URL


def get_cache_paths() -> dict:
    """Возвращает пути для кэширования"""
    cache_dir = settings.CACHE_DIR
    return {
        "CACHE_DIR": cache_dir,
        "GAMES_CACHE": os.path.join(cache_dir, "games_cache.pkl"),
        "METADATA_CACHE": os.path.join(cache_dir, "metadata_cache.pkl"),
        "BALANCED_RECOMMENDATIONS_CACHE": os.path.join(cache_dir, "balanced_recommendations_cache.pkl"),
        "RECOMMENDATION_MATRIX_CACHE": os.path.join(cache_dir, "recommendation_matrix_cache.pkl"),
        "CACHE_INFO": os.path.join(cache_dir, "cache_info.json")
    }


def is_development() -> bool:
    """Проверяет, запущено ли приложение в режиме разработки"""
    return settings.DEBUG


def get_cors_settings() -> dict:
    """Возвращает настройки CORS"""
    return {
        "allow_origins": settings.ALLOWED_ORIGINS,
        "allow_credentials": True,
        "allow_methods": ["*"],
        "allow_headers": ["*"]
    }
