# Развертывание в продакшене

Этот документ описывает, как правильно развернуть систему рекомендаций игр в продакшене.

## Подготовка к развертыванию

### 1. Проверка конфигурации
```bash
# Проверьте текущую конфигурацию
python scripts/check_config.py
```

### 2. Создание продакшен конфигурации
```bash
# Создайте .env файл для продакшена
cp .env.example .env.production

# Сгенерируйте безопасный секретный ключ
python scripts/generate_secret_key.py
```

### 3. Настройка переменных окружения для продакшена
Отредактируйте `.env.production`:

```env
# Безопасность
SECRET_KEY=your-super-secure-production-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# База данных
DATABASE_URL=sqlite:///src/database.db
DB_PATH=data/games.db

# Сервер
HOST=0.0.0.0
PORT=8000
DEBUG=false

# CORS - укажите конкретные домены!
ALLOWED_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Кэш
CACHE_DIR=src/cache

# Внешние API
STEAM_API_TIMEOUT=10.0

# Настройки приложения
APP_TITLE=Система рекомендаций игр
APP_DESCRIPTION=Асинхронный API для рекомендации игр с использованием гибридного метода рекомендаций
```

## Развертывание с Docker

### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Установка зависимостей
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Копирование кода
COPY . .

# Создание директорий
RUN mkdir -p src/cache data logs

# Переменные окружения
ENV PYTHONPATH=/app
ENV DEBUG=false

# Порт
EXPOSE 8000

# Команда запуска
CMD ["python", "run.py"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    env_file:
      - .env.production
    volumes:
      - ./data:/app/data
      - ./src/cache:/app/src/cache
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

### Запуск с Docker
```bash
# Сборка и запуск
docker-compose up -d

# Проверка логов
docker-compose logs -f app

# Остановка
docker-compose down
```

## Развертывание на VPS

### 1. Подготовка сервера
```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Установка Python и зависимостей
sudo apt install python3 python3-pip python3-venv nginx -y

# Создание пользователя для приложения
sudo useradd -m -s /bin/bash gameapp
sudo su - gameapp
```

### 2. Развертывание приложения
```bash
# Клонирование репозитория
git clone https://github.com/yourusername/game-recommendation-system.git
cd game-recommendation-system

# Создание виртуального окружения
python3 -m venv venv
source venv/bin/activate

# Установка зависимостей
pip install -r requirements.txt

# Генерация секретного ключа
python scripts/generate_secret_key.py

# Настройка переменных окружения для продакшена
export SECRET_KEY=your-generated-secret-key-here
export DEBUG=false
export HOST=0.0.0.0
export PORT=8000
export ALLOWED_ORIGINS=https://yourdomain.com

# Проверка конфигурации
python scripts/check_config.py

# Запуск в продакшен режиме
python scripts/run_production.py
```

### 3. Настройка systemd сервиса
Создайте файл `/etc/systemd/system/gameapp.service`:

```ini
[Unit]
Description=Game Recommendation System
After=network.target

[Service]
Type=simple
User=gameapp
Group=gameapp
WorkingDirectory=/home/<USER>/game-recommendation-system
Environment=PATH=/home/<USER>/game-recommendation-system/venv/bin
ExecStart=/home/<USER>/game-recommendation-system/venv/bin/python run.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Запуск сервиса
sudo systemctl daemon-reload
sudo systemctl enable gameapp
sudo systemctl start gameapp
sudo systemctl status gameapp
```

### 4. Настройка Nginx
Создайте файл `/etc/nginx/sites-available/gameapp`:

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# Активация конфигурации
sudo ln -s /etc/nginx/sites-available/gameapp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Настройка SSL (Let's Encrypt)

```bash
# Установка Certbot
sudo apt install certbot python3-certbot-nginx -y

# Получение сертификата
sudo certbot --nginx -d yourdomain.com

# Автоматическое обновление
sudo crontab -e
# Добавьте строку:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## Мониторинг и логирование

### 1. Настройка логирования
Добавьте в `.env`:
```env
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### 2. Ротация логов
Создайте файл `/etc/logrotate.d/gameapp`:
```
/home/<USER>/game-recommendation-system/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 gameapp gameapp
}
```

### 3. Мониторинг с помощью systemd
```bash
# Просмотр логов
sudo journalctl -u gameapp -f

# Статус сервиса
sudo systemctl status gameapp
```

## Резервное копирование

### Скрипт резервного копирования
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/gameapp"
APP_DIR="/home/<USER>/game-recommendation-system"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Резервное копирование базы данных
cp $APP_DIR/src/database.db $BACKUP_DIR/database_$DATE.db
cp $APP_DIR/data/games.db $BACKUP_DIR/games_$DATE.db

# Резервное копирование конфигурации
cp $APP_DIR/.env $BACKUP_DIR/env_$DATE

# Удаление старых резервных копий (старше 30 дней)
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "env_*" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Автоматическое резервное копирование
```bash
# Добавьте в crontab
sudo crontab -e
# 0 2 * * * /home/<USER>/backup.sh
```

## Безопасность

### 1. Firewall
```bash
# Настройка UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. Обновления безопасности
```bash
# Автоматические обновления
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 3. Мониторинг безопасности
- Регулярно обновляйте зависимости: `pip list --outdated`
- Мониторьте логи на подозрительную активность
- Используйте fail2ban для защиты от брутфорс атак

## Проверка развертывания

```bash
# Проверка доступности API
curl -f http://yourdomain.com/docs

# Проверка конфигурации
python scripts/check_config.py

# Проверка производительности
python tests/run_tests.py --performance
```
