"""add_balanced_recommendations_table

Revision ID: b4073cb9bf59
Revises: ba91953420f7
Create Date: 2025-05-04 12:41:49.556300

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b4073cb9bf59'
down_revision: Union[str, None] = 'ba91953420f7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_balanced_recommendations_app_id', table_name='balanced_recommendations')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_balanced_recommendations_app_id', 'balanced_recommendations', ['app_id'], unique=False)
    # ### end Alembic commands ###
