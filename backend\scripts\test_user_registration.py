"""
Скрипт для тестирования регистрации пользователя
"""

import sys
import os
import time
import httpx

# Добавляем корневую директорию в путь для импорта модулей
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_user_registration():
    """Тестирует регистрацию пользователя"""
    print("🧪 Тестирование регистрации пользователя")
    print("=" * 40)
    
    # Данные для тестового пользователя
    test_user = {
        "username": f"test_user_{int(time.time())}",
        "email": f"test_{int(time.time())}@example.com",
        "password": "test_password_123"
    }
    
    print(f"👤 Тестовый пользователь:")
    print(f"   Username: {test_user['username']}")
    print(f"   Email: {test_user['email']}")
    print()
    
    try:
        # Проверяем доступность приложения
        print("🔍 Проверка доступности приложения...")
        response = httpx.get("http://127.0.0.1:8000/", timeout=5)
        if response.status_code not in [200, 307]:
            print(f"❌ Приложение недоступно (код: {response.status_code})")
            print("💡 Убедитесь, что приложение запущено: python run.py")
            return False
        print("✅ Приложение доступно")
        
        # Тестируем регистрацию
        print("\n📝 Попытка регистрации...")
        response = httpx.post(
            "http://127.0.0.1:8000/auth/register",
            json=test_user,
            timeout=10
        )
        
        print(f"📊 Код ответа: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Регистрация успешна!")
            print(f"   Ответ сервера: {data}")
            
            if data.get("success"):
                print("🎉 Пользователь успешно зарегистрирован!")
                return True
            else:
                print("⚠️  Сервер вернул success=False")
                return False
        else:
            print("❌ Ошибка регистрации!")
            try:
                error_data = response.json()
                print(f"   Детали ошибки: {error_data}")
            except:
                print(f"   Текст ошибки: {response.text}")
            return False
            
    except httpx.TimeoutException:
        print("❌ Таймаут при подключении к приложению")
        print("💡 Убедитесь, что приложение запущено и отвечает")
        return False
    except httpx.ConnectError:
        print("❌ Не удалось подключиться к приложению")
        print("💡 Убедитесь, что приложение запущено на http://127.0.0.1:8000")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {str(e)}")
        return False

def test_user_login(username: str, password: str):
    """Тестирует вход пользователя"""
    print("\n🔐 Тестирование входа пользователя...")
    
    try:
        login_data = {
            "username": username,
            "password": password
        }
        
        response = httpx.post(
            "http://127.0.0.1:8000/auth/token",
            data=login_data,  # Используем data для form-encoded
            timeout=10
        )
        
        print(f"📊 Код ответа: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            if token:
                print("✅ Вход успешен!")
                print(f"   Токен получен: {token[:20]}...")
                return token
            else:
                print("⚠️  Токен не получен")
                return None
        else:
            print("❌ Ошибка входа!")
            try:
                error_data = response.json()
                print(f"   Детали ошибки: {error_data}")
            except:
                print(f"   Текст ошибки: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Ошибка при входе: {str(e)}")
        return None

def test_user_profile(token: str):
    """Тестирует получение профиля пользователя"""
    print("\n👤 Тестирование получения профиля...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = httpx.get(
            "http://127.0.0.1:8000/auth/users/me",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Код ответа: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Профиль получен!")
            print(f"   Username: {data.get('username')}")
            print(f"   Email: {data.get('email')}")
            print(f"   Has avatar: {data.get('has_avatar', False)}")
            return True
        else:
            print("❌ Ошибка получения профиля!")
            try:
                error_data = response.json()
                print(f"   Детали ошибки: {error_data}")
            except:
                print(f"   Текст ошибки: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при получении профиля: {str(e)}")
        return False

def main():
    """Основная функция"""
    print("🧪 Комплексный тест пользовательской системы")
    print("=" * 50)
    
    # Тест регистрации
    if not test_user_registration():
        print("\n💥 Тест регистрации провален!")
        return 1
    
    # Получаем данные последнего тестового пользователя
    test_username = f"test_user_{int(time.time())}"
    test_password = "test_password_123"
    
    # Тест входа
    token = test_user_login(test_username, test_password)
    if not token:
        print("\n💥 Тест входа провален!")
        return 1
    
    # Тест профиля
    if not test_user_profile(token):
        print("\n💥 Тест профиля провален!")
        return 1
    
    print("\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
    print("Система пользователей работает корректно.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
