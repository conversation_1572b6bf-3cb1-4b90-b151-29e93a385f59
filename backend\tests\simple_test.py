import os
import sys

# Add the current directory to the Python path
sys.path.append(os.path.abspath('.'))

from sqlmodel import SQLModel, create_engine

# Create a simple engine for testing
DATABASE_URL = "sqlite:///test.db"
engine = create_engine(DATABASE_URL, echo=True)

# Create a simple model for testing
class TestModel(SQLModel, table=True):
    __tablename__ = "test_table"
    
    id: int = SQLModel.Field(primary_key=True)
    name: str

# Create the tables
def create_tables():
    SQLModel.metadata.create_all(engine)
    print("Tables created successfully")

if __name__ == "__main__":
    create_tables()
    print("Test completed successfully")
