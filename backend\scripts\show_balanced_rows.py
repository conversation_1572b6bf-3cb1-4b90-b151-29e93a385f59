from sqlmodel import Session, text
from src.database import engine

def show_balanced_rows(limit=5):
    with Session(engine) as session:
        try:
            # Проверяем существование таблицы
            table_exists = session.exec(text("SELECT name FROM sqlite_master WHERE type='table' AND name='balanced_recommendations'")).first()
            if not table_exists:
                print("Таблица balanced_recommendations не существует в базе данных")
                return
            
            # Получаем первые несколько строк
            print(f"Первые {limit} строк таблицы balanced_recommendations:")
            query = text(f"SELECT * FROM balanced_recommendations LIMIT {limit}")
            rows = session.exec(query).all()
            
            if rows:
                # Получаем имена столбцов
                columns = session.exec(text("PRAGMA table_info(balanced_recommendations)")).all()
                column_names = [col[1] for col in columns]  # col[1] содержит имя столбца
                
                # Выводим заголовки столбцов
                print(" | ".join(column_names))
                print("-" * 80)
                
                # Выводим данные
                for row in rows:
                    print(" | ".join(str(value) for value in row))
            else:
                print("Таблица пуста")
                
        except Exception as e:
            print(f"Ошибка при получении данных: {str(e)}")

if __name__ == "__main__":
    show_balanced_rows()