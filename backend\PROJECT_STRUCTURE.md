# Структура проекта Game Recommendation System

## Итоговая организация файлов

Проект представляет собой чистый бэкенд API для системы рекомендаций игр Steam. Все файлы распределены по логическим папкам, зависимости актуализированы.

## Основные директории

### 📁 src/ - Основной исходный код
```
src/
├── api/                    # REST API эндпоинты
│   ├── __init__.py         # Инициализация API роутера
│   ├── endpoints.py        # Все API эндпоинты (игры, рекомендации, пользователи)
│   └── steamapi.py         # Интеграция с Steam API
├── models/                 # Модели данных
│   └── models.py           # SQLModel модели для всех таблиц БД
├── cache/                  # Кэшированные данные для быстродействия
│   ├── *.pkl              # Сериализованные данные (игры, метаданные, рекомендации)
│   └── cache_info.json    # Информация о кэше
├── auth.py                 # Система аутентификации (собственная реализация JWT)
├── database.py             # Настройки подключения к БД
├── game_recommender_db.py  # Основная логика рекомендательной системы
├── game_lists.py           # Управление списками игр пользователей
├── user_ratings.py         # Управление оценками игр пользователями
├── main.py                 # FastAPI приложение (точка входа)
├── migrate_db.py           # Утилиты миграции БД
└── README.md               # Документация исходного кода
```



### 📁 scripts/ - Утилиты и скрипты администрирования
```
scripts/
├── check_*.py              # Скрипты проверки состояния БД
├── migrate_*.py            # Скрипты миграции данных
├── show_*.py               # Скрипты просмотра данных БД
├── cache_to_db.py          # Перенос кэша в БД
├── test_user_registration.py # Тест регистрации пользователей
└── README.md               # Документация скриптов
```

### 📁 tests/ - Комплексная система тестирования
```
tests/
├── test_comprehensive.py   # Полные функциональные тесты всех API
├── test_performance.py     # Тесты производительности и нагрузки
├── run_tests.py            # Главный скрипт запуска всех тестов
├── test_app.py             # Базовые тесты приложения
├── test_auth.py            # Тесты системы аутентификации
├── test_game_id_endpoint.py # Тесты поиска игр по ID
├── simple_test.py          # Простые тесты SQLModel
├── demo_game_id_search.py  # Демонстрация поиска игр
├── simple_endpoint_test.py # Простые тесты эндпоинтов
├── __init__.py             # Инициализация пакета тестов
└── README.md               # Документация тестирования
```

### 📁 data/ - Данные
```
data/
└── games.db                # База данных игр
```

### 📁 migrations/ - Миграции Alembic
```
migrations/
├── versions/               # Файлы миграций
├── env.py                  # Настройки Alembic
└── script.py.mako          # Шаблон миграций
```

### 📁 archive/ - Архивные файлы
```
archive/
├── curs/                   # Учебные материалы
└── README.md               # Описание архива
```

## Корневые файлы

- `run.py` - Точка входа для запуска приложения
- `requirements.txt` - Актуализированные зависимости Python (39 пакетов)
- `alembic.ini` - Настройки миграций Alembic
- `README.md` - Основная документация проекта
- `PROJECT_STRUCTURE.md` - Структура проекта (этот файл)
- `DEPENDENCIES_UPDATE.md` - Документация обновления зависимостей

## Последние изменения

### ✅ Ранее выполненные изменения
- Перемещены утилитарные скрипты в `scripts/`
- Создана комплексная система тестирования в `tests/`
- Архивные материалы перемещены в `archive/`
- Добавлены README.md файлы в каждую папку
- Создана документация структуры проекта

## Преимущества новой структуры

1. **Логическая организация** - файлы сгруппированы по назначению
2. **Легкая навигация** - понятно где искать нужный код
3. **Документированность** - каждая папка имеет описание
4. **Чистота** - архивные и временные файлы вынесены отдельно
5. **Масштабируемость** - легко добавлять новые модули

## Рекомендации по разработке

- Новые API эндпоинты добавляйте в `src/api/`
- Новые модели данных - в `src/models/models.py`
- Утилитарные скрипты - в `scripts/`
- Тесты - в `tests/`
- Документацию обновляйте в соответствующих README.md
