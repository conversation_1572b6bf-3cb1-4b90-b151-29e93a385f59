"""
Скрипт для отображения всех таблиц в базе данных SQLite
"""
import os
import sqlite3

# Пути к базам данных
DB_PATHS = [
    "src/database.db",
    "data/games.db"
]

def main():
    """Основная функция"""
    print("Поиск таблиц в базах данных...")
    
    for db_path in DB_PATHS:
        if os.path.exists(db_path):
            print(f"\nБаза данных: {db_path}")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Получаем список всех таблиц
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"Найдено {len(tables)} таблиц:")
                    for i, table in enumerate(tables, 1):
                        table_name = table[0]
                        
                        # Получаем структуру таблицы
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = cursor.fetchall()
                        
                        # Получаем количество записей в таблице
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        
                        print(f"\n{i}. Таблица: {table_name}")
                        print(f"   Количество записей: {count}")
                        print("   Столбцы:")
                        for col in columns:
                            col_id, col_name, col_type, not_null, default_val, is_pk = col
                            pk_mark = " (PK)" if is_pk else ""
                            print(f"     - {col_name} ({col_type}){pk_mark}")
                else:
                    print("Таблицы не найдены")
                
                conn.close()
            except Exception as e:
                print(f"Ошибка при получении таблиц из базы данных: {str(e)}")
        else:
            print(f"\nБаза данных не найдена: {db_path}")

if __name__ == "__main__":
    main()
