# Scripts Directory

Эта папка содержит утилиты и скрипты для обслуживания и администрирования системы рекомендаций игр.

## Описание скриптов

### Работа с базой данных
- `check_db.py` - Проверка состояния базы данных
- `check_tables.py` - Проверка структуры таблиц
- `check_users_table.py` - Проверка таблицы пользователей
- `list_tables.py` - Вывод списка всех таблиц
- `simple_list_tables.py` - Простой вывод таблиц
- `show_tables.py` - Детальная информация о таблицах
- `sqlmodel_tables.py` - Работа с таблицами через SQLModel

### Миграции и обновления
- `migrate_users_table.py` - Миграция таблицы пользователей
- `update_users_table.py` - Обновление структуры таблицы пользователей

### Работа с рекомендациями
- `count_balanced_recommendations.py` - Подсчет сбалансированных рекомендаций
- `create_recommendation_matrix.py` - Создание матрицы рекомендаций
- `show_balanced_rows.py` - Показ сбалансированных строк
- `show_users.py` - Показ информации о пользователях

### Импорт и кэширование данных
- `cache_to_db.py` - Перенос данных из кэша в базу данных

### Тестирование
- `test_app.py` - Тесты основного приложения
- `test_auth.py` - Тесты системы аутентификации
- `simple_test.py` - Простые тесты

## Использование

Большинство скриптов можно запускать напрямую из корневой директории проекта:

```bash
python scripts/script_name.py
```

Убедитесь, что у вас установлены все зависимости из `requirements.txt` перед запуском скриптов.

## Примечания

- Некоторые скрипты могут изменять данные в базе данных, используйте их осторожно
- Рекомендуется делать резервные копии базы данных перед запуском скриптов миграции
- Скрипты тестирования безопасны для запуска в любое время
