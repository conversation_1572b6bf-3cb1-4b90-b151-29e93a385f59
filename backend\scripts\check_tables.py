"""
Проверка создания таблиц в базе данных
"""
import os
import sys
import sqlite3

# Путь к базе данных
DB_PATH = "src/database.db"

def main():
    """Основная функция проверки таблиц"""
    print("Проверка таблиц в базе данных...")
    
    if not os.path.exists(DB_PATH):
        print(f"База данных не найдена по пути: {DB_PATH}")
        return
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Получаем список всех таблиц
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"Найдено {len(tables)} таблиц:")
    for i, table in enumerate(tables, 1):
        print(f"{i}. {table[0]}")
        
        # Получаем структуру таблицы
        cursor.execute(f"PRAGMA table_info({table[0]})")
        columns = cursor.fetchall()
        
        print(f"   Структура таблицы {table[0]}:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # Получаем количество записей в таблице
        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
        count = cursor.fetchone()[0]
        print(f"   Количество записей: {count}")
        print()
    
    conn.close()
    print("Проверка завершена!")

if __name__ == "__main__":
    main()
