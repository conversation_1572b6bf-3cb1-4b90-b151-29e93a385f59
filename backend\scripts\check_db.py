"""
Проверка базы данных SQLite
"""
import os
import sqlite3

def main():
    """Основная функция проверки базы данных"""
    # Ищем все файлы .db в текущей директории и поддиректориях
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_path = os.path.join(root, file)
                db_files.append(db_path)
    
    if not db_files:
        print("Файлы баз данных SQLite не найдены")
        return
    
    print(f"Найдено {len(db_files)} файлов баз данных SQLite:")
    for i, db_path in enumerate(db_files, 1):
        print(f"{i}. {db_path}")
        check_db(db_path)
        print()

def check_db(db_path):
    """Проверка базы данных"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Получаем список всех таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"   Найдено {len(tables)} таблиц:")
        for table in tables:
            table_name = table[0]
            
            # Получаем количество записей в таблице
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            print(f"   - {table_name} ({count} записей)")
        
        conn.close()
    except Exception as e:
        print(f"   Ошибка при проверке базы данных: {str(e)}")

if __name__ == "__main__":
    main()
