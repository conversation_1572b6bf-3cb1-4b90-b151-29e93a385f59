#!/usr/bin/env python3
"""
Скрипт для проверки конфигурации переменных окружения
"""
import os
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.config import settings, is_development


def check_secret_key():
    """Проверяет секретный ключ"""
    print("🔐 Проверка секретного ключа...")
    
    if settings.SECRET_KEY == "your-secret-key-here":
        print("❌ КРИТИЧЕСКАЯ ОШИБКА: Используется секретный ключ по умолчанию!")
        print("   Сгенерируйте новый ключ: python scripts/generate_secret_key.py")
        return False
    
    if len(settings.SECRET_KEY) < 16:
        print("⚠️  ПРЕДУПРЕЖДЕНИЕ: Секретный ключ слишком короткий (менее 16 символов)")
        return False
    
    print("✅ Секретный ключ настроен корректно")
    return True


def check_database():
    """Проверяет настройки базы данных"""
    print("\n🗄️  Проверка базы данных...")
    
    # Проверяем URL базы данных
    if not settings.DATABASE_URL:
        print("❌ ОШИБКА: DATABASE_URL не установлен")
        return False
    
    print(f"✅ DATABASE_URL: {settings.DATABASE_URL}")
    
    # Проверяем путь к базе данных игр
    if not os.path.exists(settings.DB_PATH):
        print(f"⚠️  ПРЕДУПРЕЖДЕНИЕ: База данных игр не найдена: {settings.DB_PATH}")
        print("   Возможно, нужно загрузить данные игр")
    else:
        print(f"✅ База данных игр найдена: {settings.DB_PATH}")
    
    return True


def check_server_settings():
    """Проверяет настройки сервера"""
    print("\n🌐 Проверка настроек сервера...")
    
    print(f"✅ Хост: {settings.HOST}")
    print(f"✅ Порт: {settings.PORT}")
    print(f"✅ Режим отладки: {'включен' if settings.DEBUG else 'выключен'}")
    
    # Проверяем порт
    if settings.PORT < 1 or settings.PORT > 65535:
        print("❌ ОШИБКА: Некорректный порт")
        return False
    
    return True


def check_cors_settings():
    """Проверяет настройки CORS"""
    print("\n🔗 Проверка настроек CORS...")
    
    if settings.ALLOWED_ORIGINS == ["*"] and not is_development():
        print("⚠️  ПРЕДУПРЕЖДЕНИЕ: В продакшене используется CORS='*'")
        print("   Рекомендуется указать конкретные домены")
    
    print(f"✅ Разрешенные домены: {settings.ALLOWED_ORIGINS}")
    return True


def check_cache_settings():
    """Проверяет настройки кэша"""
    print("\n💾 Проверка настроек кэша...")
    
    cache_dir = Path(settings.CACHE_DIR)
    if not cache_dir.exists():
        print(f"⚠️  ПРЕДУПРЕЖДЕНИЕ: Директория кэша не существует: {cache_dir}")
        print("   Будет создана автоматически при первом запуске")
    else:
        print(f"✅ Директория кэша: {cache_dir}")
    
    return True


def check_env_file():
    """Проверяет наличие .env файла"""
    print("📄 Проверка .env файла...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  ПРЕДУПРЕЖДЕНИЕ: Файл .env не найден")
        print("   Скопируйте .env.example в .env и настройте переменные")
        return False
    
    print("✅ Файл .env найден")
    return True


def check_production_readiness():
    """Проверяет готовность к продакшену"""
    print("\n🚀 Проверка готовности к продакшену...")
    
    issues = []
    
    if settings.DEBUG:
        issues.append("Режим отладки включен")
    
    if settings.SECRET_KEY == "your-secret-key-here":
        issues.append("Используется секретный ключ по умолчанию")
    
    if settings.ALLOWED_ORIGINS == ["*"]:
        issues.append("CORS разрешен для всех доменов")
    
    if issues:
        print("❌ НЕ ГОТОВО к продакшену:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print("✅ Готово к продакшену")
    return True


def main():
    """Основная функция"""
    print("🔍 Проверка конфигурации системы рекомендаций игр")
    print("=" * 60)
    
    all_checks_passed = True
    
    # Проверяем .env файл
    if not check_env_file():
        all_checks_passed = False
    
    # Основные проверки
    checks = [
        check_secret_key,
        check_database,
        check_server_settings,
        check_cors_settings,
        check_cache_settings,
    ]
    
    for check in checks:
        if not check():
            all_checks_passed = False
    
    # Проверка готовности к продакшену
    production_ready = check_production_readiness()
    
    print("\n" + "=" * 60)
    
    if all_checks_passed:
        print("✅ Все проверки пройдены успешно!")
    else:
        print("❌ Обнаружены проблемы в конфигурации")
    
    if is_development():
        print("🔧 Режим: РАЗРАБОТКА")
    else:
        print("🚀 Режим: ПРОДАКШЕН")
        if not production_ready:
            print("⚠️  Есть проблемы для продакшена!")
    
    print("\n💡 Для получения помощи см. docs/ENVIRONMENT_SETUP.md")
    
    return 0 if all_checks_passed else 1


if __name__ == "__main__":
    sys.exit(main())
