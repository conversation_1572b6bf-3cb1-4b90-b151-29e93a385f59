"""
Тестирование функциональности приложения
"""
import os
import sys

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

from sqlmodel import Session, select
from src.database import create_db_and_tables, engine
from src.models.models import User, UserRating, GameList, GameListType, Game
from src.auth import get_password_hash

def main():
    """Основная функция тестирования"""
    print("Начинаем тестирование приложения...")
    
    # Создаем таблицы
    print("\n1. Создание таблиц в базе данных")
    create_db_and_tables()
    print("✓ Таблицы успешно созданы")
    
    # Создаем тестового пользователя
    print("\n2. Создание тестового пользователя")
    create_test_user()
    
    # Получаем список игр
    print("\n3. Получение списка игр")
    games = get_games()
    
    if not games:
        print("❌ В базе данных нет игр. Тестирование невозможно продолжить.")
        return
    
    # Добавляем оценку игре
    print("\n4. Добавление оценки игре")
    add_game_rating(games[0])
    
    # Добавляем игру в список "играю"
    print("\n5. Добавление игры в список 'играю'")
    add_game_to_list(games[0], GameListType.PLAYING)
    
    # Если есть вторая игра, добавляем ее в список "в планах"
    if len(games) > 1:
        print("\n6. Добавление игры в список 'в планах'")
        add_game_to_list(games[1], GameListType.PLANNED)
    
    # Если есть третья игра, добавляем ее в список "прошел"
    if len(games) > 2:
        print("\n7. Добавление игры в список 'прошел'")
        add_game_to_list(games[2], GameListType.COMPLETED)
    
    # Получаем оценки пользователя
    print("\n8. Получение оценок пользователя")
    get_user_ratings()
    
    # Получаем списки игр пользователя
    print("\n9. Получение списков игр пользователя")
    get_user_game_lists()
    
    print("\nТестирование успешно завершено!")

def create_test_user():
    """Создание тестового пользователя"""
    with Session(engine) as session:
        # Проверяем, существует ли пользователь
        user = session.exec(select(User).where(User.username == "testuser")).first()
        
        if user:
            print(f"✓ Пользователь {user.username} уже существует (ID: {user.id})")
        else:
            # Создаем нового пользователя
            hashed_password = get_password_hash("testpassword")
            new_user = User(
                username="testuser",
                email="<EMAIL>",
                hashed_password=hashed_password,
                is_active=True
            )
            session.add(new_user)
            session.commit()
            session.refresh(new_user)
            print(f"✓ Создан пользователь {new_user.username} (ID: {new_user.id})")

def get_games():
    """Получение списка игр из базы данных"""
    with Session(engine) as session:
        games = session.exec(select(Game).limit(5)).all()
        
        if not games:
            print("❌ В базе данных нет игр")
            return []
        
        print(f"✓ Найдено {len(games)} игр:")
        for i, game in enumerate(games[:5], 1):
            print(f"  {i}. {game.title} (ID: {game.app_id})")
        
        return games

def add_game_rating(game):
    """Добавление оценки игре"""
    with Session(engine) as session:
        # Получаем тестового пользователя
        user = session.exec(select(User).where(User.username == "testuser")).first()
        
        if not user:
            print("❌ Тестовый пользователь не найден")
            return
        
        # Проверяем, существует ли уже оценка
        existing_rating = session.exec(
            select(UserRating).where(
                (UserRating.user_id == user.id) & 
                (UserRating.app_id == game.app_id)
            )
        ).first()
        
        if existing_rating:
            # Обновляем существующую оценку
            existing_rating.rating = 5
            session.add(existing_rating)
            session.commit()
            print(f"✓ Обновлена оценка для игры '{game.title}': 5/5")
        else:
            # Добавляем новую оценку
            new_rating = UserRating(user_id=user.id, app_id=game.app_id, rating=5)
            session.add(new_rating)
            session.commit()
            print(f"✓ Добавлена оценка для игры '{game.title}': 5/5")

def add_game_to_list(game, list_type):
    """Добавление игры в список"""
    with Session(engine) as session:
        # Получаем тестового пользователя
        user = session.exec(select(User).where(User.username == "testuser")).first()
        
        if not user:
            print("❌ Тестовый пользователь не найден")
            return
        
        # Проверяем, существует ли уже запись в списке
        existing_list = session.exec(
            select(GameList).where(
                (GameList.user_id == user.id) & 
                (GameList.app_id == game.app_id)
            )
        ).first()
        
        list_type_names = {
            GameListType.PLAYING: "играю",
            GameListType.PLANNED: "в планах",
            GameListType.COMPLETED: "прошел"
        }
        
        if existing_list:
            # Обновляем существующую запись
            existing_list.list_type = list_type
            session.add(existing_list)
            session.commit()
            print(f"✓ Игра '{game.title}' перемещена в список '{list_type_names[list_type]}'")
        else:
            # Добавляем новую запись
            new_list_item = GameList(
                user_id=user.id,
                app_id=game.app_id,
                list_type=list_type
            )
            session.add(new_list_item)
            session.commit()
            print(f"✓ Игра '{game.title}' добавлена в список '{list_type_names[list_type]}'")

def get_user_ratings():
    """Получение оценок пользователя"""
    with Session(engine) as session:
        # Получаем тестового пользователя
        user = session.exec(select(User).where(User.username == "testuser")).first()
        
        if not user:
            print("❌ Тестовый пользователь не найден")
            return
        
        # Получаем оценки пользователя
        ratings_query = select(UserRating, Game).join(Game).where(UserRating.user_id == user.id)
        ratings_result = session.exec(ratings_query).all()
        
        if not ratings_result:
            print("❌ У пользователя нет оценок")
            return
        
        print(f"✓ Найдено {len(ratings_result)} оценок:")
        for rating, game in ratings_result:
            print(f"  • {game.title}: {rating.rating}/5")

def get_user_game_lists():
    """Получение списков игр пользователя"""
    with Session(engine) as session:
        # Получаем тестового пользователя
        user = session.exec(select(User).where(User.username == "testuser")).first()
        
        if not user:
            print("❌ Тестовый пользователь не найден")
            return
        
        # Получаем списки игр пользователя
        lists_query = select(GameList, Game).join(Game).where(GameList.user_id == user.id)
        lists_result = session.exec(lists_query).all()
        
        if not lists_result:
            print("❌ У пользователя нет списков игр")
            return
        
        # Группируем по типу списка
        playing = []
        planned = []
        completed = []
        
        for list_item, game in lists_result:
            if list_item.list_type == GameListType.PLAYING:
                playing.append(game.title)
            elif list_item.list_type == GameListType.PLANNED:
                planned.append(game.title)
            elif list_item.list_type == GameListType.COMPLETED:
                completed.append(game.title)
        
        print("✓ Списки игр пользователя:")
        print(f"  • Играю ({len(playing)}): {', '.join(playing) if playing else 'нет игр'}")
        print(f"  • В планах ({len(planned)}): {', '.join(planned) if planned else 'нет игр'}")
        print(f"  • Прошел ({len(completed)}): {', '.join(completed) if completed else 'нет игр'}")

if __name__ == "__main__":
    main()
