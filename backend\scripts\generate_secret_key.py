#!/usr/bin/env python3
"""
Скрипт для генерации безопасного секретного ключа для JWT токенов
"""
import secrets
import string


def generate_secret_key(length: int = 32) -> str:
    """
    Генерирует безопасный секретный ключ
    
    Args:
        length: Длина ключа (по умолчанию 32 символа)
        
    Returns:
        Безопасный секретный ключ
    """
    return secrets.token_urlsafe(length)


def generate_complex_secret_key(length: int = 64) -> str:
    """
    Генерирует сложный секретный ключ с различными символами
    
    Args:
        length: Длина ключа (по умолчанию 64 символа)
        
    Returns:
        Сложный секретный ключ
    """
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def main():
    """Основная функция"""
    print("🔐 Генератор секретных ключей для JWT токенов")
    print("=" * 50)
    
    # Генерируем простой ключ
    simple_key = generate_secret_key()
    print(f"Простой ключ (32 символа):")
    print(f"SECRET_KEY={simple_key}")
    print()
    
    # Генерируем сложный ключ
    complex_key = generate_complex_secret_key()
    print(f"Сложный ключ (64 символа):")
    print(f"SECRET_KEY={complex_key}")
    print()
    
    # Генерируем ключ для продакшена
    production_key = generate_secret_key(48)
    print(f"Ключ для продакшена (48 символов):")
    print(f"SECRET_KEY={production_key}")
    print()
    
    print("💡 Рекомендации:")
    print("1. Скопируйте один из ключей выше")
    print("2. Добавьте его в файл .env как SECRET_KEY=ваш_ключ")
    print("3. Никогда не коммитьте .env файл в систему контроля версий")
    print("4. Используйте разные ключи для разработки и продакшена")
    print("5. Регулярно меняйте ключи в продакшене")


if __name__ == "__main__":
    main()
