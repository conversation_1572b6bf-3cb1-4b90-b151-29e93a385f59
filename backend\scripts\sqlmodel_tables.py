"""
Скрипт для отображения всех таблиц в базе данных с использованием SQLModel
"""
import os
import sys

# Добавляем текущую директорию в путь Python
sys.path.append(os.path.abspath('.'))

from sqlmodel import SQLModel, create_engine, inspect
from src.models.models import *

# Пути к базам данных
DB_PATHS = [
    "sqlite:///src/database.db",
    "sqlite:///data/games.db"
]

def main():
    """Основная функция"""
    print("Поиск таблиц в базах данных...")
    
    for db_url in DB_PATHS:
        print(f"\nБаза данных: {db_url}")
        try:
            # Создаем движок для базы данных
            engine = create_engine(db_url)
            
            # Получаем инспектор для базы данных
            inspector = inspect(engine)
            
            # Получаем список всех таблиц
            tables = inspector.get_table_names()
            
            if tables:
                print(f"Найдено {len(tables)} таблиц:")
                for i, table_name in enumerate(tables, 1):
                    # Получаем структуру таблицы
                    columns = inspector.get_columns(table_name)
                    
                    # Получаем первичные ключи
                    pk_columns = inspector.get_pk_constraint(table_name)['constrained_columns']
                    
                    print(f"\n{i}. Таблица: {table_name}")
                    print("   Столбцы:")
                    for col in columns:
                        col_name = col['name']
                        col_type = str(col['type'])
                        pk_mark = " (PK)" if col_name in pk_columns else ""
                        print(f"     - {col_name} ({col_type}){pk_mark}")
                    
                    # Получаем внешние ключи
                    foreign_keys = inspector.get_foreign_keys(table_name)
                    if foreign_keys:
                        print("   Внешние ключи:")
                        for fk in foreign_keys:
                            referred_table = fk['referred_table']
                            constrained_columns = fk['constrained_columns']
                            referred_columns = fk['referred_columns']
                            print(f"     - {', '.join(constrained_columns)} -> {referred_table}({', '.join(referred_columns)})")
            else:
                print("Таблицы не найдены")
        except Exception as e:
            print(f"Ошибка при получении таблиц из базы данных: {str(e)}")

if __name__ == "__main__":
    main()
