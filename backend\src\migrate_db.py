"""
Миграция базы данных для совместимости с SQLModel
"""
import os
import sqlite3
from sqlmodel import SQLModel, create_engine

# Пути к базам данных
DB_PATHS = [
    "src/database.db",
    "data/games.db"
]

def migrate_users_table(db_path):
    """Миграция таблицы users"""
    if not os.path.exists(db_path):
        print(f"База данных не найдена: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Проверяем, существует ли таблица users
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print(f"Таблица users не найдена в базе данных {db_path}")
            conn.close()
            return

        # Получаем текущую структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        print(f"Текущая структура таблицы users в {db_path}:")
        for col in columns:
            print(f"- {col[1]} ({col[2]})")

        # Проверяем, есть ли колонка is_active
        if "is_active" not in column_names:
            print("Добавление колонки is_active...")
            cursor.execute("ALTER TABLE users ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT 1")
            print("Колонка is_active успешно добавлена")
        else:
            print("Колонка is_active уже существует")

        # Проверяем, есть ли колонка hashed_password
        if "hashed_password" not in column_names:
            print("Добавление колонки hashed_password...")
            cursor.execute("ALTER TABLE users ADD COLUMN hashed_password TEXT")
            print("Колонка hashed_password успешно добавлена")
        else:
            print("Колонка hashed_password уже существует")

        # Получаем обновленную структуру таблицы users
        cursor.execute("PRAGMA table_info(users)")
        updated_columns = cursor.fetchall()

        print(f"Обновленная структура таблицы users:")
        for col in updated_columns:
            print(f"- {col[1]} ({col[2]})")

        conn.commit()
        conn.close()
        print(f"Таблица users в базе данных {db_path} успешно обновлена")
    except Exception as e:
        print(f"Ошибка при миграции таблицы users: {str(e)}")

def create_user_ratings_table(db_path):
    """Создание таблицы user_ratings"""
    if not os.path.exists(db_path):
        print(f"База данных не найдена: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Проверяем, существует ли таблица user_ratings
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_ratings'")
        if cursor.fetchone():
            print(f"Таблица user_ratings уже существует в базе данных {db_path}")
            conn.close()
            return

        # Создаем таблицу user_ratings
        cursor.execute('''
        CREATE TABLE user_ratings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            app_id INTEGER NOT NULL,
            rating INTEGER NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            UNIQUE(user_id, app_id)
        )
        ''')

        print(f"Таблица user_ratings успешно создана в базе данных {db_path}")
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Ошибка при создании таблицы user_ratings: {str(e)}")

def create_game_lists_table(db_path):
    """Создание таблицы game_lists"""
    if not os.path.exists(db_path):
        print(f"База данных не найдена: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Проверяем, существует ли таблица game_lists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='game_lists'")
        if cursor.fetchone():
            print(f"Таблица game_lists уже существует в базе данных {db_path}")
            conn.close()
            return

        # Создаем таблицу game_lists
        cursor.execute('''
        CREATE TABLE game_lists (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            app_id INTEGER NOT NULL,
            list_type TEXT NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            UNIQUE(user_id, app_id)
        )
        ''')

        print(f"Таблица game_lists успешно создана в базе данных {db_path}")
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Ошибка при создании таблицы game_lists: {str(e)}")

def main():
    """Основная функция миграции"""
    print("Начинаем миграцию базы данных...")

    for db_path in DB_PATHS:
        print(f"\nОбработка базы данных: {db_path}")
        migrate_users_table(db_path)
        create_user_ratings_table(db_path)
        create_game_lists_table(db_path)

    print("\nМиграция базы данных завершена")

if __name__ == "__main__":
    main()
