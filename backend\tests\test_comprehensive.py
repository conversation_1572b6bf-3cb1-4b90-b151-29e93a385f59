"""
Комплексные тесты для системы рекомендаций игр Steam
Тестирует все основные функции приложения
"""

import sys
import os
import asyncio
import json
import time
from typing import Dict, Any, List
import httpx

# Добавляем корневую директорию в путь для импорта модулей
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.main import app
from fastapi.testclient import TestClient

class GameRecommendationTester:
    """Класс для комплексного тестирования системы рекомендаций игр"""

    def __init__(self):
        self.client = TestClient(app)
        self.base_url = "http://testserver"
        self.test_user_data = {
            "username": "test_user_" + str(int(time.time())),
            "email": f"test_{int(time.time())}@example.com",
            "password": "test_password_123"
        }
        self.access_token = None
        self.test_results = []

    def log_test(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """Логирование результатов тестов"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        if not success and details:
            print(f"    Details: {details}")
        print()

    def test_health_check(self):
        """Тест доступности приложения"""
        try:
            response = self.client.get("/")
            success = response.status_code in [200, 307]  # 307 для редиректа
            self.log_test(
                "Health Check",
                success,
                f"Status code: {response.status_code}",
                response.headers.get("location") if response.status_code == 307 else None
            )
            return success
        except Exception as e:
            self.log_test("Health Check", False, f"Exception: {str(e)}")
            return False

    def test_user_registration(self):
        """Тест регистрации пользователя"""
        try:
            response = self.client.post("/auth/register", json=self.test_user_data)
            success = response.status_code == 200

            if success:
                data = response.json()
                success = data.get("success", False)
                message = f"User registered: {data.get('username')}"
            else:
                message = f"Registration failed: {response.status_code}"

            self.log_test("User Registration", success, message, response.json() if response.status_code != 500 else None)
            return success
        except Exception as e:
            self.log_test("User Registration", False, f"Exception: {str(e)}")
            return False

    def test_user_login(self):
        """Тест входа пользователя"""
        try:
            login_data = {
                "username": self.test_user_data["username"],
                "password": self.test_user_data["password"]
            }
            response = self.client.post("/auth/token", data=login_data)
            success = response.status_code == 200

            if success:
                data = response.json()
                self.access_token = data.get("access_token")
                success = self.access_token is not None
                message = "Login successful, token received"
            else:
                message = f"Login failed: {response.status_code}"

            self.log_test("User Login", success, message)
            return success
        except Exception as e:
            self.log_test("User Login", False, f"Exception: {str(e)}")
            return False

    def test_user_profile(self):
        """Тест получения профиля пользователя"""
        if not self.access_token:
            self.log_test("User Profile", False, "No access token available")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.get("/auth/users/me", headers=headers)
            success = response.status_code == 200

            if success:
                data = response.json()
                username = data.get("username")
                message = f"Profile retrieved for user: {username}"
            else:
                message = f"Profile retrieval failed: {response.status_code}"

            self.log_test("User Profile", success, message)
            return success
        except Exception as e:
            self.log_test("User Profile", False, f"Exception: {str(e)}")
            return False

    def test_games_list(self):
        """Тест получения списка игр"""
        try:
            response = self.client.get("/games?limit=10")
            success = response.status_code == 200

            if success:
                data = response.json()
                games_count = len(data) if isinstance(data, list) else 0
                message = f"Retrieved {games_count} games"
            else:
                message = f"Games list failed: {response.status_code}"

            self.log_test("Games List", success, message)
            return success
        except Exception as e:
            self.log_test("Games List", False, f"Exception: {str(e)}")
            return False

    def test_game_search(self):
        """Тест поиска игр"""
        try:
            response = self.client.get("/games/search/Counter")
            success = response.status_code == 200

            if success:
                data = response.json()
                results_count = len(data) if isinstance(data, list) else 0
                message = f"Search returned {results_count} results"
            else:
                message = f"Game search failed: {response.status_code}"

            self.log_test("Game Search", success, message)
            return success
        except Exception as e:
            self.log_test("Game Search", False, f"Exception: {str(e)}")
            return False

    def test_autocomplete(self):
        """Тест автокомплита"""
        try:
            response = self.client.get("/games/autocomplete?query=Counter&limit=5")
            success = response.status_code == 200

            if success:
                data = response.json()
                suggestions_count = len(data) if isinstance(data, list) else 0
                message = f"Autocomplete returned {suggestions_count} suggestions"
            else:
                message = f"Autocomplete failed: {response.status_code}"

            self.log_test("Autocomplete", success, message)
            return success
        except Exception as e:
            self.log_test("Autocomplete", False, f"Exception: {str(e)}")
            return False

    def test_game_recommendations(self):
        """Тест получения рекомендаций"""
        try:
            response = self.client.get("/recommend/Counter-Strike?top_n=5")
            success = response.status_code == 200

            if success:
                data = response.json()
                recommendations = data.get("recommendations", [])
                message = f"Got {len(recommendations)} recommendations"
            else:
                message = f"Recommendations failed: {response.status_code}"

            self.log_test("Game Recommendations", success, message)
            return success
        except Exception as e:
            self.log_test("Game Recommendations", False, f"Exception: {str(e)}")
            return False

    def test_game_details(self):
        """Тест получения детальной информации об игре"""
        try:
            # Используем популярную игру Counter-Strike (app_id: 10)
            response = self.client.get("/games/10")
            success = response.status_code == 200

            if success:
                data = response.json()
                title = data.get("title", "Unknown")
                has_image = "image_url" in data
                message = f"Game details for '{title}', image: {has_image}"
            else:
                message = f"Game details failed: {response.status_code}"

            self.log_test("Game Details", success, message)
            return success
        except Exception as e:
            self.log_test("Game Details", False, f"Exception: {str(e)}")
            return False

    def test_game_filters(self):
        """Тест фильтрации игр"""
        try:
            response = self.client.get("/games/filter?year_min=2020&rating=Very Positive")
            success = response.status_code == 200

            if success:
                data = response.json()
                filtered_count = len(data) if isinstance(data, list) else 0
                message = f"Filter returned {filtered_count} games"
            else:
                message = f"Game filter failed: {response.status_code}"

            self.log_test("Game Filters", success, message)
            return success
        except Exception as e:
            self.log_test("Game Filters", False, f"Exception: {str(e)}")
            return False

    def test_user_rating(self):
        """Тест добавления оценки игры"""
        if not self.access_token:
            self.log_test("User Rating", False, "No access token available")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            rating_data = {"app_id": 10, "rating": 5}
            response = self.client.post("/auth/users/me/ratings", json=rating_data, headers=headers)
            success = response.status_code == 200

            if success:
                data = response.json()
                message = f"Rating added successfully"
            else:
                message = f"Rating failed: {response.status_code}"

            self.log_test("User Rating", success, message)
            return success
        except Exception as e:
            self.log_test("User Rating", False, f"Exception: {str(e)}")
            return False

    def test_game_list_management(self):
        """Тест управления списками игр"""
        if not self.access_token:
            self.log_test("Game List Management", False, "No access token available")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}

            # Добавляем игру в список "playing"
            response = self.client.post("/auth/users/me/game-lists/playing/10", headers=headers)
            success = response.status_code == 200

            if success:
                # Получаем списки пользователя
                response = self.client.get("/auth/users/me/game-lists", headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    playing_games = data.get("playing", [])
                    message = f"Game list management successful, {len(playing_games)} games in 'playing'"
                else:
                    success = False
                    message = "Failed to retrieve game lists"
            else:
                message = f"Game list management failed: {response.status_code}"

            self.log_test("Game List Management", success, message)
            return success
        except Exception as e:
            self.log_test("Game List Management", False, f"Exception: {str(e)}")
            return False

    def test_personal_recommendations(self):
        """Тест персональных рекомендаций"""
        if not self.access_token:
            self.log_test("Personal Recommendations", False, "No access token available")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.get("/auth/users/me/recommendations?top_n=5", headers=headers)
            success = response.status_code == 200

            if success:
                data = response.json()
                recommendations = data.get("recommendations", [])
                message = f"Personal recommendations: {len(recommendations)} games"
            else:
                message = f"Personal recommendations failed: {response.status_code}"

            self.log_test("Personal Recommendations", success, message)
            return success
        except Exception as e:
            self.log_test("Personal Recommendations", False, f"Exception: {str(e)}")
            return False

    def run_all_tests(self):
        """Запуск всех тестов"""
        print("🚀 Запуск комплексного тестирования системы рекомендаций игр Steam")
        print("=" * 70)
        print()

        # Список всех тестов
        tests = [
            ("Проверка доступности", self.test_health_check),
            ("Регистрация пользователя", self.test_user_registration),
            ("Вход пользователя", self.test_user_login),
            ("Профиль пользователя", self.test_user_profile),
            ("Список игр", self.test_games_list),
            ("Поиск игр", self.test_game_search),
            ("Автокомплит", self.test_autocomplete),
            ("Рекомендации игр", self.test_game_recommendations),
            ("Детали игры", self.test_game_details),
            ("Фильтрация игр", self.test_game_filters),
            ("Оценка игры", self.test_user_rating),
            ("Управление списками", self.test_game_list_management),
            ("Персональные рекомендации", self.test_personal_recommendations),
        ]

        # Выполнение тестов
        passed = 0
        failed = 0

        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ CRITICAL ERROR in {test_name}: {str(e)}")
                failed += 1

        # Итоговый отчет
        print("=" * 70)
        print("📊 ИТОГОВЫЙ ОТЧЕТ")
        print("=" * 70)
        print(f"✅ Пройдено тестов: {passed}")
        print(f"❌ Провалено тестов: {failed}")
        print(f"📈 Процент успеха: {(passed / (passed + failed) * 100):.1f}%")
        print()

        # Детальная статистика
        if failed > 0:
            print("❌ ПРОВАЛИВШИЕСЯ ТЕСТЫ:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   • {result['test']}: {result['message']}")
            print()

        # Сохранение отчета в файл
        self.save_test_report()

        return failed == 0

    def save_test_report(self):
        """Сохранение детального отчета в файл"""
        try:
            report = {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "summary": {
                    "total_tests": len(self.test_results),
                    "passed": len([r for r in self.test_results if r["success"]]),
                    "failed": len([r for r in self.test_results if not r["success"]]),
                },
                "test_results": self.test_results
            }

            report_file = f"tests/test_report_{int(time.time())}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            print(f"📄 Детальный отчет сохранен: {report_file}")
        except Exception as e:
            print(f"⚠️  Не удалось сохранить отчет: {str(e)}")


def main():
    """Основная функция для запуска тестов"""
    print("🎮 Game Recommendation System - Comprehensive Testing")
    print("Система комплексного тестирования приложения")
    print()

    tester = GameRecommendationTester()
    success = tester.run_all_tests()

    if success:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        exit_code = 0
    else:
        print("💥 НЕКОТОРЫЕ ТЕСТЫ ПРОВАЛИЛИСЬ!")
        print("Проверьте детали выше и исправьте проблемы.")
        exit_code = 1

    print()
    print("Для получения помощи запустите: python tests/test_comprehensive.py --help")

    return exit_code


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h"]:
        print("""
🎮 Game Recommendation System - Comprehensive Testing

ИСПОЛЬЗОВАНИЕ:
    python tests/test_comprehensive.py

ОПИСАНИЕ:
    Комплексный тест всех функций системы рекомендаций игр Steam.
    Тестирует API эндпоинты, аутентификацию, рекомендации и пользовательские функции.

ТЕСТЫ ВКЛЮЧАЮТ:
    • Проверка доступности приложения
    • Регистрация и аутентификация пользователей
    • Поиск и фильтрация игр
    • Система рекомендаций
    • Управление списками игр
    • Оценки игр пользователями
    • Персональные рекомендации

РЕЗУЛЬТАТЫ:
    • Выводятся в консоль в реальном времени
    • Сохраняются в JSON файл для детального анализа
    • Возвращается код выхода (0 = успех, 1 = ошибки)

ТРЕБОВАНИЯ:
    • Запущенное приложение (python run.py)
    • Доступная база данных
    • Установленные зависимости
        """)
        sys.exit(0)

    sys.exit(main())
