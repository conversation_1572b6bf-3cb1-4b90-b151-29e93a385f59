#!/usr/bin/env python3
"""
Скрипт для запуска приложения в продакшен режиме
"""
import os
import sys
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.insert(0, str(Path(__file__).parent.parent))

def main():
    """Запуск в продакшен режиме"""
    print("🚀 Запуск в продакшен режиме")
    print("=" * 40)
    
    # Устанавливаем переменные окружения для продакшена
    os.environ['DEBUG'] = 'false'
    os.environ['HOST'] = '0.0.0.0'
    os.environ['PORT'] = '8000'
    
    # Проверяем, установлен ли SECRET_KEY
    if not os.getenv('SECRET_KEY') or os.getenv('SECRET_KEY') == 'your-secret-key-here':
        print("❌ ОШИБКА: SECRET_KEY не установлен!")
        print("Сгенерируйте ключ: python scripts/generate_secret_key.py")
        print("И установите его в переменных окружения:")
        print("export SECRET_KEY=your-generated-key")
        return 1
    
    # Проверяем CORS настройки
    if not os.getenv('ALLOWED_ORIGINS') or os.getenv('ALLOWED_ORIGINS') == '*':
        print("⚠️  ПРЕДУПРЕЖДЕНИЕ: CORS разрешен для всех доменов!")
        print("Рекомендуется установить конкретные домены:")
        print("export ALLOWED_ORIGINS=https://yourdomain.com")
    
    try:
        # Очищаем кэш модулей
        modules_to_clear = [mod for mod in sys.modules.keys() if mod.startswith('src.config')]
        for mod in modules_to_clear:
            del sys.modules[mod]
        
        from src.config import settings
        
        print(f"✅ Хост: {settings.HOST}")
        print(f"✅ Порт: {settings.PORT}")
        print(f"✅ Режим отладки: {'включен' if settings.DEBUG else 'выключен'}")
        print(f"✅ CORS: {settings.ALLOWED_ORIGINS}")
        
        if settings.DEBUG:
            print("❌ ПРЕДУПРЕЖДЕНИЕ: Режим отладки включен в продакшене!")
        
        print("\n🚀 Запуск сервера...")
        
        # Импортируем и запускаем приложение
        import uvicorn
        from src.main import app
        
        uvicorn.run(
            app, 
            host=settings.HOST, 
            port=settings.PORT,
            access_log=True,
            server_header=False,
            date_header=False
        )
        
    except Exception as e:
        print(f"❌ Ошибка запуска: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
